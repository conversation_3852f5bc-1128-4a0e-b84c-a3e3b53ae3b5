import { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import { 
  FaUsers, 
  FaGamepad, 
  FaTicketAlt, 
  FaFlag, 
  FaChartBar,
  FaShieldAlt,
  FaCog,
  FaExclamationTriangle
} from 'react-icons/fa';
import axios from 'axios';
import { API_URL } from '../config/env';

// Import dashboard sections
import DashboardOverview from '../components/admin/DashboardOverview';
import UserManagement from '../components/admin/UserManagement';
import GameModeration from '../components/admin/GameModeration';
import TicketManagement from '../components/admin/TicketManagement';
import ReportManagement from '../components/admin/ReportManagement';
import ModerationLogs from '../components/admin/ModerationLogs';
import SystemSettings from '../components/admin/SystemSettings';

const AdminDashboard = () => {
  const { user, loading: authLoading } = useAuth();
  const navigate = useNavigate();
  const [activeSection, setActiveSection] = useState('overview');
  const [dashboardStats, setDashboardStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);


  // Check if user has admin/moderator access
  useEffect(() => {
    // Don't redirect while still loading
    if (authLoading) {
      return;
    }

    if (!user) {
      navigate('/');
      return;
    }

    if (!user.role || !['admin', 'moderator'].includes(user.role)) {
      navigate('/');
      return;
    }

  }, [user, authLoading, navigate]);

  // Fetch dashboard statistics
  useEffect(() => {
    const fetchDashboardStats = async () => {
      try {
        setLoading(true);
        const response = await axios.get(`${API_URL}/moderation/dashboard/stats`, {
          withCredentials: true
        });
        setDashboardStats(response.data);
      } catch (error) {
        console.error('Error fetching dashboard stats:', error);
        setError('Failed to load dashboard statistics');
      } finally {
        setLoading(false);
      }
    };

    if (user && ['admin', 'moderator'].includes(user.role)) {
      fetchDashboardStats();
    }
  }, [user]);

  // Navigation items based on user role
  const getNavigationItems = () => {
    const baseItems = [
      { id: 'overview', label: 'Overview', icon: FaChartBar },
      { id: 'tickets', label: 'Tickets', icon: FaTicketAlt },
      { id: 'reports', label: 'Reports', icon: FaFlag },
      { id: 'games', label: 'Game Moderation', icon: FaGamepad },
      { id: 'logs', label: 'Moderation Logs', icon: FaShieldAlt }
    ];

    // Admin and moderator items
    if (user?.role === 'admin' || user?.role === 'moderator') {
      baseItems.splice(1, 0, { id: 'users', label: 'User Management', icon: FaUsers });
    }

    // Admin-only items
    if (user?.role === 'admin') {
      baseItems.push({ id: 'settings', label: 'System Settings', icon: FaCog });
    }

    return baseItems;
  };

  // Render active section content
  const renderContent = () => {
    switch (activeSection) {
      case 'overview':
        return <DashboardOverview stats={dashboardStats} loading={loading} />;
      case 'users':
        return (user?.role === 'admin' || user?.role === 'moderator') ? <UserManagement /> : <div>Access Denied</div>;
      case 'games':
        return <GameModeration />;
      case 'tickets':
        return <TicketManagement />;
      case 'reports':
        return <ReportManagement />;
      case 'logs':
        return <ModerationLogs />;
      case 'settings':
        return user?.role === 'admin' ? <SystemSettings /> : <div>Access Denied</div>;
      default:
        return <DashboardOverview stats={dashboardStats} loading={loading} />;
    }
  };

  if (!user || !['admin', 'moderator'].includes(user.role)) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <FaExclamationTriangle className="text-red-500 text-6xl mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-white mb-2">Access Denied</h1>
          <p className="text-gray-400">You don't have permission to access this page.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900">
      <div className="flex">
        {/* Sidebar Navigation */}
        <div className="w-64 bg-gray-800 min-h-screen border-r border-gray-700">
          <div className="p-6">
            <h1 className="text-2xl font-bold text-white mb-2">
              {user.role === 'admin' ? 'Admin' : 'Moderator'} Dashboard
            </h1>
            <p className="text-gray-400 text-sm">
              Welcome back, {user.username}
            </p>
          </div>

          <nav className="mt-6">
            {getNavigationItems().map((item) => (
              <button
                key={item.id}
                onClick={() => setActiveSection(item.id)}
                className={`w-full flex items-center gap-3 px-6 py-3 text-left transition-colors duration-200 ${
                  activeSection === item.id
                    ? 'bg-blue-600 text-white border-r-4 border-blue-400'
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                }`}
              >
                <item.icon className="text-lg" />
                {item.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-6">
          {error && (
            <div className="bg-red-900 bg-opacity-50 border border-red-600 text-red-200 p-4 rounded-lg mb-6">
              {error}
            </div>
          )}
          
          {renderContent()}
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
