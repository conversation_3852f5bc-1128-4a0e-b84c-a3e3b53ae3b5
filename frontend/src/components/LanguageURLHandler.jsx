import { useEffect, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useLanguage } from '../context/LanguageContext';

/**
 * Component that handles URL-based language detection and updates
 * This component should be placed inside the Router but outside of Routes
 */
const LanguageURLHandler = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { currentLanguage, changeLanguageOnly, availableLanguages } = useLanguage();

  // Use ref to track if we're in the middle of a language change to prevent loops
  const changingLanguageRef = useRef(false);
  const lastLanguageRef = useRef(currentLanguage);

  // Define admin routes that should not have language prefixes
  const adminRoutes = ['/admin'];

  // Handle URL changes and language context synchronization
  useEffect(() => {
    if (changingLanguageRef.current) return;

    // Check if current path is an admin route - do this first!
    const isAdminRoute = adminRoutes.some(route =>
      location.pathname === route || location.pathname.startsWith(route + '/')
    );

    // Skip ALL language handling for admin routes
    if (isAdminRoute) {
      return;
    }

    // Extract language from URL path
    const segments = location.pathname.split('/').filter(Boolean);
    const firstSegment = segments[0];
    const urlLanguage = firstSegment && availableLanguages[firstSegment] ? firstSegment : null;

    if (urlLanguage) {
      // URL has language prefix
      if (urlLanguage !== currentLanguage) {
        // Update context language to match URL
        changingLanguageRef.current = true;
        changeLanguageOnly(urlLanguage).finally(() => {
          changingLanguageRef.current = false;
          lastLanguageRef.current = urlLanguage;
        });
      }
    } else {
      // URL doesn't have language prefix, redirect to include current language
      // (This won't run for admin routes due to early return above)
      const cleanPath = location.pathname === '/' ? '' : location.pathname;
      const newPath = `/${currentLanguage}${cleanPath}`;
      navigate(newPath, { replace: true });
    }
  }, [location.pathname, currentLanguage, changeLanguageOnly, navigate, availableLanguages]);

  // Handle language changes from language selector
  useEffect(() => {
    if (changingLanguageRef.current) return;
    if (lastLanguageRef.current === currentLanguage) return;

    // Check if current path is an admin route
    const isAdminRoute = adminRoutes.some(route =>
      location.pathname === route || location.pathname.startsWith(route + '/')
    );

    // Skip language handling for admin routes
    if (isAdminRoute) {
      return;
    }

    // Extract language from URL path
    const segments = location.pathname.split('/').filter(Boolean);
    const urlLanguage = segments[0] && availableLanguages[segments[0]] ? segments[0] : null;

    // Language was changed via selector, update URL
    if (urlLanguage !== currentLanguage) {
      if (segments[0] && availableLanguages[segments[0]]) {
        // Replace existing language in URL
        segments[0] = currentLanguage;
        const newPath = '/' + segments.join('/');
        navigate(newPath, { replace: true });
      } else {
        // Add language prefix to current path
        const cleanPath = location.pathname === '/' ? '' : location.pathname;
        const newPath = `/${currentLanguage}${cleanPath}`;
        navigate(newPath, { replace: true });
      }

      lastLanguageRef.current = currentLanguage;
    }
  }, [currentLanguage, location.pathname, navigate, availableLanguages]);

  // This component doesn't render anything
  return null;
};

export default LanguageURLHandler;
