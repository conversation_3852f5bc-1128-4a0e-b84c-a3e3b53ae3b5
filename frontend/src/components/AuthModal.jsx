import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { FaTimes, FaDiscord } from 'react-icons/fa';
import { useAuth } from '../context/AuthContext';
import { useLanguage } from '../context/LanguageContext';
import { useNotification } from '../context/NotificationContext';
import { useLanguageNavigation } from '../hooks/useLanguageNavigation';
import LoadingSpinner from './LoadingSpinner';
import { googleLogin } from '../services/googleAuthService';
import { getDiscordOAuthUrl } from '../services/discordAuthService';

/**
 * AuthModal Component - Modal for login and registration
 * @param {boolean} isOpen - Whether the modal is open
 * @param {Function} onClose - Callback to close the modal
 * @param {string} initialMode - Initial mode: 'login' or 'register'
 */
const AuthModal = ({ isOpen, onClose, initialMode = 'login' }) => {
  const [mode, setMode] = useState(initialMode);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    username: '',
    confirmPassword: '',
    agreeTerms: false
  });
  const [isLoading, setIsLoading] = useState(false);
  const [formError, setFormError] = useState('');
  
  const { login, register } = useAuth();
  const { t } = useLanguage();
  const { showSuccess, showError } = useNotification();
  const { navigate } = useLanguageNavigation();

  // Reset form when modal opens/closes or mode changes
  useEffect(() => {
    if (isOpen) {
      setMode(initialMode);
      setFormData({
        email: '',
        password: '',
        username: '',
        confirmPassword: '',
        agreeTerms: false
      });
      setFormError('');
      setIsLoading(false);
    }
  }, [isOpen, initialMode]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setFormError('');

    try {
      if (mode === 'login') {
        if (!formData.email || !formData.password) {
          setFormError('Please fill in all fields');
          setIsLoading(false);
          return;
        }


        const user = await login({
          email: formData.email,
          password: formData.password
        });


        // Show success notification
        showSuccess(`Welcome back${user?.username ? `, ${user.username}` : ''}! 🎮`);

        onClose();
        navigate('/');
      } else {
        // Register mode
        if (!formData.username || !formData.email || !formData.password || !formData.confirmPassword) {
          setFormError('Please fill in all fields');
          setIsLoading(false);
          return;
        }

        if (formData.password !== formData.confirmPassword) {
          setFormError('Passwords do not match');
          setIsLoading(false);
          return;
        }

        if (!formData.agreeTerms) {
          setFormError('Please agree to the terms and conditions');
          setIsLoading(false);
          return;
        }

        

        await register({
          username: formData.username,
          email: formData.email,
          password: formData.password
        });


        // Show success notification
        showSuccess(
          `Welcome to IndieRepo, ${formData.username}! 🎉 Check your email for a welcome message.`,
          7000 // Show for 7 seconds
        );

        onClose();
        navigate('/');
      }
    } catch (error) {
      console.error('Auth error:', error);
      const errorMessage = error.message || `${mode === 'login' ? 'Login' : 'Registration'} failed. Please try again.`;
      setFormError(errorMessage);

      // Show error notification
      showError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleLogin = async () => {
    try {
      setFormError('');
      
      if (!window.google || !window.google.accounts) {
        console.error('Google API not loaded');
        setFormError('Google API not loaded. Please try again later.');
        return;
      }

      const auth2 = window.google.accounts.oauth2;
      
      const tokenClient = auth2.initTokenClient({
        client_id: import.meta.env.VITE_GOOGLE_CLIENT_ID,
        scope: 'email profile',
        callback: async (tokenResponse) => {
          if (tokenResponse.access_token) {
            try {
              const result = await googleLogin(tokenResponse.access_token);

              // The googleLogin service returns { user, token }, not { success, user }
              if (result && result.user) {
                await login(result.user, true, true);
                onClose();
                navigate('/');
              } else {
                console.error('Google login result missing user data:', result);
                throw new Error('Google login failed - no user data received');
              }
            } catch (error) {
              console.error('Google login callback error:', error);
              setFormError(error.message || 'Google login failed. Please try again.');
            }
          } else {
            console.error('No access token received from Google');
            setFormError('Google login failed. Please try again.');
          }
        }
      });
      
      tokenClient.requestAccessToken();
      
    } catch (error) {
      console.error('Google login exception:', error);
      setFormError('Google login failed. Please try again.');
    }
  };

  const handleDiscordLogin = () => {
    try {
      setFormError('');
      const authUrl = getDiscordOAuthUrl();
      window.location.href = authUrl;
    } catch (error) {
      console.error('Discord login error:', error);
      setFormError('Discord login failed. Please try again.');
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-[10000] p-4" onClick={onClose}>
      <div className="bg-gray-800 rounded-xl max-w-lg w-full max-h-[90vh] overflow-y-auto relative shadow-2xl" onClick={e => e.stopPropagation()}>
        {/* Close button */}
        <button 
          className="absolute top-4 right-4 text-gray-400 hover:text-white text-xl p-2 z-10 transition-colors duration-200" 
          onClick={onClose}
        >
          <FaTimes />
        </button>

        <div className="p-8">
          {/* Header */}
          <div className="text-center mb-6">
            <h1 className="text-3xl font-bold text-white mb-2">
              {mode === 'login' ? 'Welcome Back' : 'Create Account'}
            </h1>
            <p className="text-gray-300">
              {mode === 'login' 
                ? 'Log in to discover and track your favorite indie games'
                : 'Join our community of indie game enthusiasts'
              }
            </p>
          </div>

          {/* Error message */}
          {formError && (
            <div className="bg-red-500/10 text-red-400 border border-red-500/30 px-4 py-3 rounded-lg mb-4 text-sm">
              {formError}
            </div>
          )}

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-4 mb-6">
            
            {mode === 'register' && (
              <div>
                <label htmlFor="username" className="block mb-2 text-sm text-gray-300">Username</label>
                <input
                  type="text"
                  id="username"
                  name="username"
                  placeholder="Enter your username"
                  value={formData.username}
                  onChange={handleChange}
                  className="w-full px-4 py-3 bg-white border border-gray-600 rounded-lg text-black text-base transition-all duration-300 focus:outline-none focus:border-red-500 focus:ring-2 focus:ring-red-500/20"
                />
              </div>
            )}
            
            <div>
              <label htmlFor="email" className="block mb-2 text-sm text-gray-300">Email</label>
              <input
                type="email"
                id="email"
                name="email"
                placeholder="Enter your email"
                value={formData.email}
                onChange={handleChange}
                className="w-full px-4 py-3 bg-white border border-gray-600 rounded-lg text-black text-base transition-all duration-300 focus:outline-none focus:border-red-500 focus:ring-2 focus:ring-red-500/20"
              />
            </div>
            
            <div>
              <label htmlFor="password" className="block mb-2 text-sm text-gray-300">Password</label>
              <input
                type="password"
                id="password"
                name="password"
                placeholder="Enter your password"
                value={formData.password}
                onChange={handleChange}
                className="w-full px-4 py-3 bg-white border border-gray-600 rounded-lg text-black text-base transition-all duration-300 focus:outline-none focus:border-red-500 focus:ring-2 focus:ring-red-500/20"
              />
            </div>

            {mode === 'login' && (
              <div className="flex justify-end mb-4">
                <button
                  type="button"
                  className="text-red-500 hover:text-orange-500 transition-colors duration-300 text-sm"
                  onClick={() => {
                    // TODO: Implement forgot password functionality
                    alert('Forgot password functionality coming soon!');
                  }}
                >
                  Forgot Password?
                </button>
              </div>
            )}

            {mode === 'register' && (
              <>
                <div>
                  <label htmlFor="confirmPassword" className="block mb-2 text-sm text-gray-300">Confirm Password</label>
                  <input
                    type="password"
                    id="confirmPassword"
                    name="confirmPassword"
                    placeholder="Confirm your password"
                    value={formData.confirmPassword}
                    onChange={handleChange}
                    className="w-full px-4 py-3 bg-white border border-gray-600 rounded-lg text-black text-base transition-all duration-300 focus:outline-none focus:border-red-500 focus:ring-2 focus:ring-red-500/20"
                  />
                </div>
                
                <div className="flex items-start space-x-3">
                  <input
                    type="checkbox"
                    id="agreeTerms"
                    name="agreeTerms"
                    checked={formData.agreeTerms}
                    onChange={handleChange}
                    className="mt-1 w-4 h-4 text-red-500 bg-gray-700 border-gray-600 rounded focus:ring-red-500 focus:ring-2"
                  />
                  <label htmlFor="agreeTerms" className="text-sm text-gray-300">
                    I agree to the Terms of Service and Privacy Policy
                  </label>
                </div>
              </>
            )}
            
            <button
              type="submit"
              className="w-full px-4 py-3 bg-gradient-to-r from-red-500 to-orange-500 text-white border-none rounded-full text-base font-bold cursor-pointer transition-all duration-300 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-red-500/30 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center gap-2"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <LoadingSpinner size="sm" color="white" />
                  <span>{mode === 'login' ? 'Signing in...' : 'Creating account...'}</span>
                </>
              ) : (
                mode === 'login' ? 'Sign In' : 'Create Account'
              )}
            </button>
          </form>

          {/* Social login */}
          <div className="mb-6">
            <div className="flex items-center mb-4">
              <div className="flex-1 h-px bg-gray-600"></div>
              <span className="px-4 text-gray-400 text-sm">or continue with</span>
              <div className="flex-1 h-px bg-gray-600"></div>
            </div>

            <div className="space-y-3">
              <button
                className="w-full flex items-center justify-center gap-3 px-4 py-3 bg-white text-gray-700 border border-gray-300 rounded-lg font-medium transition-all duration-300 hover:bg-gray-50 hover:shadow-md"
                onClick={handleGoogleLogin}
              >
                <img
                  src="https://developers.google.com/static/identity/images/g-logo.png"
                  alt="Google G"
                  className="w-5 h-5"
                />
                Continue with Google
              </button>
              <button
                className="w-full flex items-center justify-center gap-3 px-4 py-3 bg-indigo-600 text-white rounded-lg font-medium transition-all duration-300 hover:bg-indigo-700 hover:shadow-md"
                onClick={handleDiscordLogin}
              >
                <FaDiscord className="w-5 h-5" /> Continue with Discord
              </button>
            </div>
          </div>

          {/* Mode toggle */}
          <div className="text-center">
            <p className="text-gray-400 text-sm">
              {mode === 'login' ? "Don't have an account? " : "Already have an account? "}
              <button
                type="button"
                onClick={() => setMode(mode === 'login' ? 'register' : 'login')}
                className="text-red-500 hover:text-orange-500 transition-colors duration-300 font-medium"
              >
                {mode === 'login' ? 'Sign up' : 'Sign in'}
              </button>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

AuthModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  initialMode: PropTypes.oneOf(['login', 'register'])
};

export default AuthModal;
