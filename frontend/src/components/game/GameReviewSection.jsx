import { useState } from 'react';
import PropTypes from 'prop-types';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import StarRating from '../common/StarRating';
import ReviewCard from '../reviews/ReviewCard';

/**
 * Component for displaying and handling game reviews
 */
const GameReviewSection = ({ 
  game, 
  reviews, 
  submitReview, 
  submitting, 
  submitError, 
  submitSuccess, 
  lastAction,
  handleDeleteRequest 
}) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [newReviewData, setNewReviewData] = useState({
    title: '',
    content: '',
    rating: 0
  });

  // Calculate featured and other reviews
  const featuredReviews = reviews.filter(review => review.featured);
  const otherReviews = reviews.filter(review => !review.featured);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewReviewData({
      ...newReviewData,
      [name]: value
    });
  };

  const handleRatingChange = (newRating) => {
    setNewReviewData(prev => ({
      ...prev,
      rating: newRating
    }));
  };

  const handleSubmitReview = (e) => {
    e.preventDefault();
    submitReview(newReviewData, () => {
      // Reset form on success
      setNewReviewData({
        title: '',
        content: '',
        rating: 0
      });
    });
  };

  // Function to handle login redirects consistently
  const redirectToLogin = (returnPath) => {
    navigate('/login', { state: { from: returnPath } });
  };

  return (
    <div className="mt-12">
      <h2 className="text-3xl font-bold text-white mb-8">Reviews</h2>
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700 mb-8">
        <h3 className="text-xl font-semibold text-white mb-4">Write a Review</h3>
        {submitSuccess && (
          <div className="bg-green-900 bg-opacity-50 border border-green-600 text-green-200 p-4 rounded-lg mb-4">
            Your review has been {lastAction} successfully!
          </div>
        )}
        {submitError && (
          <div className="bg-red-900 bg-opacity-50 border border-red-600 text-red-200 p-4 rounded-lg mb-4">
            {submitError}
          </div>
        )}
        <form onSubmit={handleSubmitReview} className="space-y-4">
          <div>
            <label htmlFor="title" className="block text-white font-medium mb-2">Review Title:</label>
            <input 
              type="text" 
              id="title" 
              name="title" 
              value={newReviewData.title} 
              onChange={handleInputChange} 
              placeholder="Summarize your thoughts..." 
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              required 
            />
          </div>
          <div>
            <label htmlFor="content" className="block text-white font-medium mb-2">Review:</label>
            <textarea 
              id="content" 
              name="content" 
              value={newReviewData.content} 
              onChange={handleInputChange} 
              placeholder="Share your experience with this game..." 
              rows="5" 
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent resize-vertical"
              required 
            ></textarea>
          </div>
          <div>
            <label className="block text-white font-medium mb-2">Rating</label>
            <div className="mb-4">
              <StarRating 
                rating={newReviewData.rating} 
                setRating={handleRatingChange} 
                interactive={true} 
              />
            </div>
          </div>
          <button 
            type="submit" 
            className={`bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 disabled:from-gray-600 disabled:to-gray-600 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-200 disabled:cursor-not-allowed ${submitting ? 'opacity-75' : ''}`}
            disabled={!newReviewData.title || !newReviewData.content || newReviewData.rating === 0 || submitting}
          >
            {submitting ? 'Submitting...' : user ? 'Submit Review' : 'Login to Review'}
          </button>
          {!user && (
            <p className="text-gray-400 text-sm mt-2">
              Please <span onClick={() => redirectToLogin(`/game/${game.id}`)} className="text-green-400 cursor-pointer underline hover:text-green-300">login</span> to submit a review.
            </p>
          )}
        </form>
      </div>
      {featuredReviews.length > 0 && (
        <div className="mb-8">
          <h3 className="text-2xl font-semibold text-white mb-4">Top Reviews</h3>
          <div className="space-y-4">
            {featuredReviews.map(review => (
              <ReviewCard key={review.id} review={review} featured={true} onDelete={handleDeleteRequest} />
            ))}
          </div>
        </div>
      )}
      {otherReviews.length > 0 && (
        <div className="mb-8">
          <h3 className="text-2xl font-semibold text-white mb-4">Community Reviews</h3>
          <div className="space-y-4">
            {otherReviews.map(review => (
              <ReviewCard key={review.id} review={review} featured={false} onDelete={handleDeleteRequest} />
            ))}
          </div>
        </div>
      )}
      {reviews.length === 0 && (
        <div className="text-center py-12 bg-gray-800 rounded-lg border border-gray-700">
          <p className="text-gray-400 text-lg">No reviews yet. Be the first to review this game!</p>
        </div>
      )}
    </div>
  );
};

GameReviewSection.propTypes = {
  game: PropTypes.object.isRequired, 
  reviews: PropTypes.array.isRequired, 
  submitReview: PropTypes.func.isRequired,
  submitting: PropTypes.bool.isRequired, 
  submitError: PropTypes.string.isRequired, 
  submitSuccess: PropTypes.bool.isRequired, 
  lastAction: PropTypes.string.isRequired,
  handleDeleteRequest: PropTypes.func.isRequired
};

export default GameReviewSection; 