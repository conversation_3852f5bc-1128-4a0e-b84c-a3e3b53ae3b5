import { useState, useEffect } from 'react';
import { FaGamepad, FaTrash, FaEye, FaFlag } from 'react-icons/fa';
import axios from 'axios';
import { API_URL } from '../../config/env';
import GameLoader from '../GameLoader';

const GameModeration = () => {
  const [games, setGames] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    fetchGames();
  }, [currentPage, searchTerm, filterStatus]);

  const fetchGames = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage,
        limit: 20,
        search: searchTerm,
        status: filterStatus
      });

      const response = await axios.get(`${API_URL}/moderation/games?${params}`, {
        withCredentials: true
      });

      setGames(response.data.games);
      setTotalPages(response.data.totalPages);
    } catch (error) {
      console.error('Error fetching games:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteGame = async (gameId) => {
    const reason = prompt('Please provide a reason for deleting this game:');
    if (!reason || !reason.trim()) {
      alert('A reason is required to delete a game.');
      return;
    }

    if (!confirm('Are you sure you want to delete this game?')) return;

    try {
      await axios.delete(`${API_URL}/moderation/games/${gameId}`, {
        data: { reason: reason.trim() },
        withCredentials: true
      });
      fetchGames();
    } catch (error) {
      console.error('Error deleting game:', error);
      alert('Failed to delete game: ' + (error.response?.data?.message || error.message));
    }
  };

  const handleRestoreGame = async (gameId) => {
    const reason = prompt('Please provide a reason for restoring this game:');
    if (!reason || !reason.trim()) {
      alert('A reason is required to restore a game.');
      return;
    }

    try {
      await axios.post(`${API_URL}/moderation/games/${gameId}/restore`, {
        reason: reason.trim()
      }, {
        withCredentials: true
      });
      fetchGames();
    } catch (error) {
      console.error('Error restoring game:', error);
      alert('Failed to restore game: ' + (error.response?.data?.message || error.message));
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <GameLoader size="lg" variant="gamepad" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-white mb-2">Game Moderation</h2>
        <p className="text-gray-400">Manage and moderate game content</p>
      </div>

      {/* Filters */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <input
            type="text"
            placeholder="Search games..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />

          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Games</option>
            <option value="published">Published</option>
            <option value="removed">Removed</option>
          </select>

          <button
            onClick={fetchGames}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200"
          >
            Refresh
          </button>
        </div>
      </div>

      {/* Games Table */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Game
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Developer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Uploaded
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700">
              {games.map((game) => (
                <tr key={game.id} className="hover:bg-gray-700">
                  <td className="px-6 py-4">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-12 w-12">
                        <div className="h-12 w-12 rounded bg-gray-600 flex items-center justify-center">
                          <FaGamepad className="text-gray-400" />
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-white">{game.title}</div>
                        <div className="text-sm text-gray-400">{game.genre}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                    {game.user?.username || 'Unknown'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full border ${
                      game.status === 'published' 
                        ? 'bg-green-900 text-green-200 border-green-600'
                        : 'bg-red-900 text-red-200 border-red-600'
                    }`}>
                      {game.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                    {new Date(game.created_at).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => window.open(`/game/${game.slug}`, '_blank')}
                        className="text-blue-400 hover:text-blue-300 flex items-center gap-1"
                      >
                        <FaEye /> View
                      </button>
                      
                      {game.status === 'published' ? (
                        <button
                          onClick={() => handleDeleteGame(game.id)}
                          className="text-red-400 hover:text-red-300 flex items-center gap-1"
                        >
                          <FaTrash /> Remove
                        </button>
                      ) : (
                        <button
                          onClick={() => handleRestoreGame(game.id)}
                          className="text-green-400 hover:text-green-300 flex items-center gap-1"
                        >
                          <FaFlag /> Restore
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex justify-between items-center">
        <div className="text-sm text-gray-400">
          Page {currentPage} of {totalPages}
        </div>
        <div className="flex gap-2">
          <button
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
            className="px-3 py-1 bg-gray-700 text-white rounded disabled:opacity-50"
          >
            Previous
          </button>
          <button
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
            className="px-3 py-1 bg-gray-700 text-white rounded disabled:opacity-50"
          >
            Next
          </button>
        </div>
      </div>
    </div>
  );
};

export default GameModeration;
