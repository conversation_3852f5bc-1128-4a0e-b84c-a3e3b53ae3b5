import PropTypes from 'prop-types';
import { FaUsers, FaGamepad, FaTicketAlt, FaFlag, FaChartLine, FaExclamationTriangle } from 'react-icons/fa';
import GameLoader from '../GameLoader';

const DashboardOverview = ({ stats, loading }) => {
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <GameLoader size="lg" variant="gamepad" />
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="text-center py-12">
        <FaExclamationTriangle className="text-red-500 text-4xl mx-auto mb-4" />
        <p className="text-gray-400">Failed to load dashboard statistics</p>
      </div>
    );
  }

  const statCards = [
    {
      title: 'Total Users',
      value: stats.users?.total || 0,
      subtitle: `${stats.users?.banned || 0} banned`,
      icon: FaUsers,
      color: 'blue',
      trend: null
    },
    {
      title: 'Total Games',
      value: stats.games?.total || 0,
      subtitle: `${stats.games?.removed || 0} removed`,
      icon: FaGamepad,
      color: 'green',
      trend: null
    },
    {
      title: 'Open Tickets',
      value: stats.tickets?.open || 0,
      subtitle: 'Require attention',
      icon: FaTicketAlt,
      color: 'yellow',
      trend: null
    },
    {
      title: 'Pending Reports',
      value: stats.reports?.pending || 0,
      subtitle: 'Need review',
      icon: FaFlag,
      color: 'red',
      trend: null
    }
  ];

  const getColorClasses = (color) => {
    const colors = {
      blue: 'bg-blue-900 bg-opacity-50 border-blue-600 text-blue-200',
      green: 'bg-green-900 bg-opacity-50 border-green-600 text-green-200',
      yellow: 'bg-yellow-900 bg-opacity-50 border-yellow-600 text-yellow-200',
      red: 'bg-red-900 bg-opacity-50 border-red-600 text-red-200'
    };
    return colors[color] || colors.blue;
  };

  const getIconColorClasses = (color) => {
    const colors = {
      blue: 'text-blue-400',
      green: 'text-green-400',
      yellow: 'text-yellow-400',
      red: 'text-red-400'
    };
    return colors[color] || colors.blue;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-white mb-2">Dashboard Overview</h2>
        <p className="text-gray-400">Monitor platform activity and moderation status</p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((card, index) => (
          <div
            key={index}
            className={`border rounded-lg p-6 ${getColorClasses(card.color)}`}
          >
            <div className="flex items-center justify-between mb-4">
              <div>
                <p className="text-sm font-medium opacity-80">{card.title}</p>
                <p className="text-3xl font-bold">{card.value.toLocaleString()}</p>
              </div>
              <card.icon className={`text-3xl ${getIconColorClasses(card.color)}`} />
            </div>
            <p className="text-sm opacity-70">{card.subtitle}</p>
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-xl font-bold text-white mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg transition-colors duration-200 flex items-center gap-2">
            <FaTicketAlt />
            View Open Tickets
          </button>
          <button className="bg-red-600 hover:bg-red-700 text-white px-4 py-3 rounded-lg transition-colors duration-200 flex items-center gap-2">
            <FaFlag />
            Review Reports
          </button>
          <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-3 rounded-lg transition-colors duration-200 flex items-center gap-2">
            <FaGamepad />
            Moderate Games
          </button>
        </div>
      </div>

      {/* Recent Activity Summary */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* User Activity */}
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <h3 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
            <FaUsers className="text-blue-400" />
            User Statistics
          </h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-gray-300">Active Users</span>
              <span className="text-white font-semibold">
                {(stats.users?.total - stats.users?.banned) || 0}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-300">Banned Users</span>
              <span className="text-red-400 font-semibold">
                {stats.users?.banned || 0}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-300">Total Reviews</span>
              <span className="text-white font-semibold">
                {stats.reviews?.total || 0}
              </span>
            </div>
          </div>
        </div>

        {/* Content Statistics */}
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <h3 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
            <FaGamepad className="text-green-400" />
            Content Statistics
          </h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-gray-300">Published Games</span>
              <span className="text-white font-semibold">
                {(stats.games?.total - stats.games?.removed) || 0}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-300">Removed Games</span>
              <span className="text-red-400 font-semibold">
                {stats.games?.removed || 0}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-300">Pending Tickets</span>
              <span className="text-yellow-400 font-semibold">
                {stats.tickets?.open || 0}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* System Health */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
          <FaChartLine className="text-purple-400" />
          System Health
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-400 mb-1">
              {stats.reports?.pending > 10 ? 'High' : stats.reports?.pending > 5 ? 'Medium' : 'Low'}
            </div>
            <div className="text-sm text-gray-400">Report Load</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-400 mb-1">
              {stats.tickets?.open > 20 ? 'High' : stats.tickets?.open > 10 ? 'Medium' : 'Low'}
            </div>
            <div className="text-sm text-gray-400">Ticket Volume</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-400 mb-1">
              {stats.users?.banned > 50 ? 'High' : stats.users?.banned > 20 ? 'Medium' : 'Low'}
            </div>
            <div className="text-sm text-gray-400">Moderation Activity</div>
          </div>
        </div>
      </div>
    </div>
  );
};

DashboardOverview.propTypes = {
  stats: PropTypes.shape({
    users: PropTypes.shape({
      total: PropTypes.number,
      banned: PropTypes.number
    }),
    games: PropTypes.shape({
      total: PropTypes.number,
      removed: PropTypes.number
    }),
    tickets: PropTypes.shape({
      open: PropTypes.number
    }),
    reports: PropTypes.shape({
      pending: PropTypes.number
    }),
    reviews: PropTypes.shape({
      total: PropTypes.number
    })
  }),
  loading: PropTypes.bool
};

export default DashboardOverview;
