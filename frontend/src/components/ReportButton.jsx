import { useState } from 'react';
import PropTypes from 'prop-types';
import { FaFlag, FaTimes } from 'react-icons/fa';
import axios from 'axios';
import { API_URL } from '../config/env';

const ReportButton = ({ type, targetId, targetTitle, className = "" }) => {
  const [showModal, setShowModal] = useState(false);
  const [reason, setReason] = useState('');
  const [description, setDescription] = useState('');
  const [loading, setLoading] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const reportReasons = [
    { value: 'offensive', label: 'Offensive Content' },
    { value: 'sexually_explicit', label: 'Sexually Explicit' },
    { value: 'disruptive', label: 'Disruptive Behavior' },
    { value: 'rule_violation', label: 'Rule Violation' }
  ];

  const handleSubmitReport = async () => {
    if (!reason) return;

    try {
      setLoading(true);
      await axios.post(`${API_URL}/reports`, {
        reported_type: type,
        reported_id: targetId,
        reason,
        description: description.trim()
      }, {
        withCredentials: true
      });

      setSubmitted(true);
      setTimeout(() => {
        setShowModal(false);
        setSubmitted(false);
        setReason('');
        setDescription('');
      }, 2000);
    } catch (error) {
      console.error('Error submitting report:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setReason('');
    setDescription('');
    setSubmitted(false);
  };

  return (
    <>
      <button
        onClick={() => setShowModal(true)}
        className={`flex items-center gap-1 text-gray-400 hover:text-red-400 transition-colors duration-200 ${className}`}
        title={`Report ${type}`}
      >
        <FaFlag size={14} />
        <span className="text-sm">Report</span>
      </button>

      {/* Report Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-lg w-full max-w-md border border-gray-700">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-bold text-white">
                  Report {type === 'game' ? 'Game' : type === 'review' ? 'Review' : 'User'}
                </h3>
                <button
                  onClick={handleCloseModal}
                  className="text-gray-400 hover:text-white"
                >
                  <FaTimes />
                </button>
              </div>

              {submitted ? (
                <div className="text-center py-8">
                  <div className="text-green-400 text-4xl mb-4">✓</div>
                  <h4 className="text-lg font-semibold text-white mb-2">Report Submitted</h4>
                  <p className="text-gray-400">Thank you for helping keep our community safe.</p>
                </div>
              ) : (
                <div className="space-y-6">
                  <div>
                    <p className="text-gray-300 mb-2">
                      You are reporting: <span className="font-semibold text-white">{targetTitle}</span>
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3">
                      Reason for report *
                    </label>
                    <div className="space-y-2">
                      {reportReasons.map((reasonOption) => (
                        <label key={reasonOption.value} className="flex items-center">
                          <input
                            type="radio"
                            name="reason"
                            value={reasonOption.value}
                            checked={reason === reasonOption.value}
                            onChange={(e) => setReason(e.target.value)}
                            className="mr-3 text-red-600 bg-gray-700 border-gray-600 focus:ring-red-500"
                          />
                          <span className="text-gray-300">{reasonOption.label}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Additional details (optional)
                    </label>
                    <textarea
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                      className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500"
                      rows="3"
                      placeholder="Provide additional context about this report..."
                      maxLength={500}
                    />
                    <div className="text-right text-xs text-gray-400 mt-1">
                      {description.length}/500
                    </div>
                  </div>

                  <div className="flex justify-end gap-3">
                    <button
                      onClick={handleCloseModal}
                      className="px-4 py-2 text-gray-400 hover:text-white transition-colors duration-200"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleSubmitReport}
                      disabled={!reason || loading}
                      className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {loading ? 'Submitting...' : 'Submit Report'}
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

ReportButton.propTypes = {
  type: PropTypes.oneOf(['game', 'review', 'user']).isRequired,
  targetId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  targetTitle: PropTypes.string.isRequired,
  className: PropTypes.string
};

export default ReportButton;
