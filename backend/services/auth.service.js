const db = require('../config/database');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const { OAuth2Client } = require('google-auth-library');
const axios = require('axios');
const googleClient = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);
const { getRandomDefaultAvatar, getDefaultAvatarById, DEFAULT_AVATARS } = require('../constants/defaultAvatars');

/**
 * Get a consistent default avatar based on user ID
 * @param {number} userId - User ID
 * @returns {Object} Avatar object with url, filename, and name
 */
const getConsistentDefaultAvatar = (userId) => {
  if (!userId) return getRandomDefaultAvatar();

  // Use user ID to consistently select the same avatar
  const avatarIndex = userId % DEFAULT_AVATARS.length;
  return DEFAULT_AVATARS[avatarIndex];
};

const authService = {
  /**
   * Register a new user
   */
  register: async (userData) => {
    try {
      // Check if user already exists
      const existingUser = await db('users')
        .where('email', userData.email.toLowerCase())
        .orWhere('username', userData.username.toLowerCase())
        .first();

      if (existingUser) {
        if (existingUser.email === userData.email.toLowerCase()) {
          throw new Error('Email already in use');
        } else {
          throw new Error('Username already taken');
        }
      }

      // Hash password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(userData.password, salt);

      // Generate referral code
      const referralCode = `REF${Math.random().toString(36).substring(2, 8).toUpperCase()}`;

      // Create new user first (without avatar)
      const [userResult] = await db('users').insert({
        username: userData.username.toLowerCase(),
        email: userData.email.toLowerCase(),
        password: hashedPassword,
        display_name: userData.displayName,
        referral_code: referralCode,
        is_verified: false,
        provider: 'local'
      }).returning('id');

      // Extract the actual ID from the result object
      const userId = userResult.id || userResult;

      // Get a consistent default avatar based on user ID
      const defaultAvatar = getConsistentDefaultAvatar(userId);

      // Update user with consistent avatar
      await db('users')
        .where('id', userId)
        .update({ profile_image: defaultAvatar.url });

      // Get the created user with updated avatar
      const user = await db('users')
        .select('id', 'username', 'email', 'role', 'profile_image', 'display_name')
        .where('id', userId)
        .first();

      // Create token for response
      const token = jwt.sign(
        { userId: user.id, username: user.username, role: user.role },
        process.env.JWT_SECRET,
        { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
      );

      return {
        token,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role,
          profileImage: user.profile_image,
          displayName: user.display_name
        }
      };
    } catch (error) {
      throw error;
    }
  },

  /**
   * Login with email and password
   */
  login: async (email, password) => {
    try {
      // Find user by email
      const user = await db('users').where('email', email.toLowerCase()).first();
      
      if (!user) {
        throw new Error('User not found');
      }

      // If user exists but is a social login user without password
      if (user.provider !== 'local' && !user.password) {
        throw new Error(`Please login with ${user.provider}`);
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        throw new Error('Invalid password');
      }

      // Create token
      const token = jwt.sign(
        { userId: user.id, username: user.username, role: user.role },
        process.env.JWT_SECRET,
        { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
      );

      return {
        token,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role,
          profileImage: user.profile_image,
          displayName: user.display_name
        }
      };
    } catch (error) {
      throw error;
    }
  },

  /**
   * Verify Google token and authenticate user
   */
  googleLogin: async (token, tokenType = 'id_token') => {
    try {
      let payload;
      
      if (tokenType === 'access_token') {
        // Fetch user info using the access token
        const response = await axios.get('https://www.googleapis.com/oauth2/v3/userinfo', {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
        
        payload = response.data;
        // Map the response to match the ID token payload structure
        payload.sub = payload.sub || payload.id; // Use sub or id for the Google ID
      } else {
        // Verify ID token
        const ticket = await googleClient.verifyIdToken({
          idToken: token,
          audience: process.env.GOOGLE_CLIENT_ID
        });
        
        payload = ticket.getPayload();
      }
      
      // Check if user exists with this Google ID
      let user = await db('users').where('google_id', payload.sub).first();
      
      if (!user) {
        // Check if user exists with this email
        user = await db('users').where('email', payload.email).first();
        
        if (user) {
          // Update existing user with Google info
          const updateData = {
            google_id: payload.sub,
            is_google_user: true,
            is_verified: true,
            provider: user.provider === 'local' ? 'both' : 'google'
          };

          // Always use consistent default avatar (ignore social media profile images)
          if (!user.profile_image) {
            updateData.profile_image = getConsistentDefaultAvatar(user.id).url;
          }

          await db('users')
            .where('id', user.id)
            .update(updateData);
          
          // Get updated user
          user = await db('users').where('id', user.id).first();
        } else {
          // Always use consistent default avatar (ignore Google profile picture)
          
          // Create new user without profile image first
          const [userResult] = await db('users').insert({
            username: `user_${payload.sub.substring(0, 8)}`,
            email: payload.email,
            google_id: payload.sub,
            display_name: payload.name,
            is_google_user: true,
            is_verified: true,
            provider: 'google',
            referral_code: `REF${Math.random().toString(36).substring(2, 8).toUpperCase()}`
          }).returning('id');

          // Extract the actual ID from the result object
          const userId = userResult.id || userResult;

          // Always assign consistent default avatar (ignore Google profile picture)
          const defaultAvatar = getConsistentDefaultAvatar(userId);
          await db('users')
            .where('id', userId)
            .update({ profile_image: defaultAvatar.url });

          // Get the created user
          user = await db('users').where('id', userId).first();
        }
      }
      
      // Generate JWT token
      const jwtToken = jwt.sign(
        { userId: user.id, username: user.username, role: user.role },
        process.env.JWT_SECRET,
        { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
      );
      
      return {
        token: jwtToken,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role,
          profileImage: user.profile_image,
          displayName: user.display_name
        }
      };
    } catch (error) {
      console.error('Google authentication error:', error);
      throw new Error('Failed to authenticate with Google');
    }
  },

  /**
   * Discord OAuth login
   */
  discordLogin: async (code, redirectUri) => {
    try {
      console.log('Discord login attempt:', {
        codeLength: code?.length,
        redirectUri,
        envRedirectUri: process.env.DISCORD_REDIRECT_URI,
        clientId: process.env.DISCORD_CLIENT_ID ? 'Set' : 'Not set',
        clientSecret: process.env.DISCORD_CLIENT_SECRET ? 'Set' : 'Not set'
      });

      if (!process.env.DISCORD_CLIENT_ID || !process.env.DISCORD_CLIENT_SECRET) {
        throw new Error('Discord OAuth credentials not configured');
      }

      // Exchange code for access token
      const tokenUrl = 'https://discord.com/api/oauth2/token';
      const finalRedirectUri = redirectUri || process.env.DISCORD_REDIRECT_URI;

      const tokenParams = {
        client_id: process.env.DISCORD_CLIENT_ID,
        client_secret: process.env.DISCORD_CLIENT_SECRET,
        grant_type: 'authorization_code',
        code: code,
        redirect_uri: finalRedirectUri
      };

      console.log('Discord token exchange params:', {
        client_id: tokenParams.client_id,
        grant_type: tokenParams.grant_type,
        redirect_uri: tokenParams.redirect_uri,
        codeLength: code?.length
      });

      const tokenResponse = await axios.post(
        tokenUrl,
        new URLSearchParams(tokenParams),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      );

      console.log('Discord token response status:', tokenResponse.status);

      if (!tokenResponse.data || !tokenResponse.data.access_token) {
        console.error('Discord token response error:', tokenResponse.data);
        throw new Error('Failed to get access token from Discord');
      }

      const { access_token } = tokenResponse.data;
      
      // Get user information with the access token
      const userResponse = await axios.get('https://discord.com/api/users/@me', {
        headers: {
          Authorization: `Bearer ${access_token}`
        }
      });

      const discordUser = userResponse.data;
      
      // Find user with discord ID
      let user = await db('users').where('discord_id', discordUser.id).first();
      
      if (!user && discordUser.email) {
        // Check if user with this email already exists
        user = await db('users').where('email', discordUser.email).first();
      }
      
      if (!user) {
        // Create new user with Discord info
        const username = discordUser.username + Math.floor(Math.random() * 1000);
        
        // Always use consistent default avatar (ignore Discord avatar)
        
        const [userResult] = await db('users').insert({
          email: discordUser.email || `discord_${discordUser.id}@placeholder.com`,
          username: username,
          display_name: discordUser.global_name || discordUser.username,
          discord_id: discordUser.id,
          provider: 'discord',
          is_verified: true
        }).returning('id');

        // Extract the actual ID from the result object
        const userId = userResult.id || userResult;

        // Always assign consistent default avatar (ignore Discord avatar)
        const defaultAvatar = getConsistentDefaultAvatar(userId);
        await db('users')
          .where('id', userId)
          .update({ profile_image: defaultAvatar.url });

        // Extract the actual ID from the result object
        user = await db('users').where('id', userId).first();
      } else {
        // Update existing user with Discord ID if needed
        if (!user.discord_id) {
          const updateData = {
            discord_id: discordUser.id,
            provider: user.provider === 'local' ? 'discord' : user.provider,
            is_verified: true
          };

          // Always use consistent default avatar (ignore social media profile images)
          if (!user.profile_image) {
            updateData.profile_image = getConsistentDefaultAvatar(user.id).url;
          }

          await db('users')
            .where('id', user.id)
            .update(updateData);
          
          user = await db('users').where('id', user.id).first();
        }
      }

      // Generate JWT token
      const token = jwt.sign(
        { userId: user.id, username: user.username, role: user.role },
        process.env.JWT_SECRET,
        { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
      );
      
      return {
        success: true,
        token,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role,
          profileImage: user.profile_image,
          displayName: user.display_name
        }
      };
    } catch (error) {
      console.error('Discord authentication error:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        stack: error.stack
      });

      let errorMessage = 'Discord authentication failed';

      if (error.response?.status === 400) {
        errorMessage = 'Invalid authorization code or redirect URI mismatch';
      } else if (error.response?.status === 401) {
        errorMessage = 'Invalid Discord client credentials';
      } else if (error.message.includes('credentials not configured')) {
        errorMessage = 'Discord OAuth not properly configured';
      }

      return {
        success: false,
        message: errorMessage
      };
    }
  },

  /**
   * Verify JWT token
   */
  verifyToken: (token) => {
    try {
      return jwt.verify(token, process.env.JWT_SECRET);
    } catch (error) {
      throw new Error('Invalid token');
    }
  },

  /**
   * Get user by ID
   */
  getUserById: async (id) => {
    try {
      const user = await db('users')
        .select('id', 'username', 'email', 'role', 'profile_image', 'display_name', 'is_verified')
        .where('id', id)
        .first();
      
      if (!user) {
        throw new Error('User not found');
      }

      return {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        profileImage: user.profile_image,
        displayName: user.display_name,
        isVerified: user.is_verified
      };
    } catch (error) {
      throw error;
    }
  }
};

module.exports = authService;
