const db = require('../config/database');

/**
 * Role hierarchy for permission checking
 */
const ROLE_HIERARCHY = {
  user: 0,
  moderator: 1,
  admin: 2
};

/**
 * Permission definitions for different actions
 */
const PERMISSIONS = {
  // User management
  BAN_USER: ['admin', 'moderator'],
  UNBAN_USER: ['admin', 'moderator'],
  PROMOTE_USER: ['admin'],
  DEMOTE_USER: ['admin'],
  VIEW_USER_DETAILS: ['admin', 'moderator'],
  
  // Game management
  DELETE_GAME: ['admin', 'moderator'],
  RESTORE_GAME: ['admin', 'moderator'],
  EDIT_GAME_DETAILS: ['admin'],
  VIEW_GAME_DETAILS: ['admin', 'moderator'],
  
  // Review management
  DELETE_REVIEW: ['admin', 'moderator'],
  EDIT_REVIEW: ['admin'],
  RESTORE_REVIEW: ['admin', 'moderator'],
  
  // Ticket management
  VIEW_ALL_TICKETS: ['admin', 'moderator'],
  ASSIGN_TICKETS: ['admin', 'moderator'],
  RESOLVE_TICKETS: ['admin', 'moderator'],
  VIEW_INTERNAL_NOTES: ['admin', 'moderator'],
  
  // Report management
  VIEW_REPORTS: ['admin', 'moderator'],
  RESOLVE_REPORTS: ['admin', 'moderator'],
  DISMISS_REPORTS: ['admin', 'moderator'],
  EDIT_REPORTS: ['admin'],
  DELETE_REPORTS: ['admin'],
  
  // System administration
  VIEW_MODERATION_LOGS: ['admin', 'moderator'],
  MANAGE_SYSTEM_SETTINGS: ['admin'],
  VIEW_SYSTEM_SETTINGS: ['admin', 'moderator'],
  EDIT_SYSTEM_SETTINGS: ['admin'],
  VIEW_ANALYTICS: ['admin', 'moderator']
};

/**
 * Check if user has required role level
 */
const hasRoleLevel = (userRole, requiredRole) => {
  const userLevel = ROLE_HIERARCHY[userRole] || 0;
  const requiredLevel = ROLE_HIERARCHY[requiredRole] || 0;
  return userLevel >= requiredLevel;
};

/**
 * Check if user has specific permission
 */
const hasPermission = (userRole, permission) => {
  const allowedRoles = PERMISSIONS[permission];
  if (!allowedRoles) return false;
  return allowedRoles.includes(userRole);
};

/**
 * Middleware to require specific role
 */
const requireRole = (requiredRole) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const userRole = req.user.role || 'user';
      
      if (!hasRoleLevel(userRole, requiredRole)) {
        return res.status(403).json({ 
          message: 'Insufficient permissions',
          required: requiredRole,
          current: userRole
        });
      }

      next();
    } catch (error) {
      console.error('Role middleware error:', error);
      res.status(500).json({ message: 'Server error during authorization' });
    }
  };
};

/**
 * Middleware to require specific permission
 */
const requirePermission = (permission) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const userRole = req.user.role || 'user';
      
      if (!hasPermission(userRole, permission)) {
        return res.status(403).json({ 
          message: 'Insufficient permissions',
          required: permission,
          current: userRole
        });
      }

      next();
    } catch (error) {
      console.error('Permission middleware error:', error);
      res.status(500).json({ message: 'Server error during authorization' });
    }
  };
};

/**
 * Middleware to check if user is banned
 */
const checkBanStatus = async (req, res, next) => {
  try {
    if (!req.user) {
      return next();
    }

    const user = await db('users')
      .select('is_banned', 'banned_until', 'ban_reason')
      .where('id', req.user.userId)
      .first();

    if (!user) {
      return res.status(401).json({ message: 'User not found' });
    }

    // Check if user is permanently banned
    if (user.is_banned && !user.banned_until) {
      return res.status(403).json({ 
        message: 'Account permanently banned',
        reason: user.ban_reason
      });
    }

    // Check if user is temporarily banned
    if (user.is_banned && user.banned_until) {
      const banExpiry = new Date(user.banned_until);
      const now = new Date();
      
      if (now < banExpiry) {
        return res.status(403).json({ 
          message: 'Account temporarily banned',
          reason: user.ban_reason,
          bannedUntil: user.banned_until
        });
      } else {
        // Ban has expired, unban the user
        await db('users')
          .where('id', req.user.userId)
          .update({
            is_banned: false,
            banned_until: null,
            ban_reason: null
          });
      }
    }

    next();
  } catch (error) {
    console.error('Ban check middleware error:', error);
    res.status(500).json({ message: 'Server error during ban check' });
  }
};

/**
 * Middleware to check if user can perform action on resource
 */
const canModifyResource = (resourceType) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const userRole = req.user.role || 'user';
      const userId = req.user.userId;

      // Admins and moderators can modify any resource
      if (hasRoleLevel(userRole, 'moderator')) {
        return next();
      }

      // Regular users can only modify their own resources
      let resourceOwnerId;
      const resourceId = req.params.id || req.params.gameId || req.params.reviewId;

      switch (resourceType) {
        case 'game':
          const game = await db('games').select('user_id').where('id', resourceId).first();
          resourceOwnerId = game?.user_id;
          break;
        case 'review':
          const review = await db('reviews').select('user_id').where('id', resourceId).first();
          resourceOwnerId = review?.user_id;
          break;
        case 'user':
          resourceOwnerId = parseInt(resourceId);
          break;
        default:
          return res.status(400).json({ message: 'Invalid resource type' });
      }

      if (resourceOwnerId !== userId) {
        return res.status(403).json({ message: 'Can only modify your own resources' });
      }

      next();
    } catch (error) {
      console.error('Resource modification check error:', error);
      res.status(500).json({ message: 'Server error during resource check' });
    }
  };
};

module.exports = {
  requireRole,
  requirePermission,
  checkBanStatus,
  canModifyResource,
  hasRoleLevel,
  hasPermission,
  ROLE_HIERARCHY,
  PERMISSIONS
};
