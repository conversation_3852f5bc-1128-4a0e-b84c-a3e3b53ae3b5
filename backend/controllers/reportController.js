const ReportService = require('../services/report.service');
const { hasPermission } = require('../middleware/roleMiddleware');
const db = require('../config/database');

/**
 * Create a new report
 */
exports.createReport = async (req, res) => {
  try {
    const {
      reportType,
      description,
      reportedUserId,
      reportedGameId,
      reportedReviewId
    } = req.body;
    const reporterId = req.user.userId;

    // Validate required fields
    if (!reportType || !description) {
      return res.status(400).json({ 
        message: 'Report type and description are required' 
      });
    }

    // Validate that at least one target is specified
    if (!reportedUserId && !reportedGameId && !reportedReviewId) {
      return res.status(400).json({ 
        message: 'Must specify what you are reporting (user, game, or review)' 
      });
    }

    // Validate report type
    const validTypes = [
      'offensive_content', 'sexually_explicit', 'harassment', 'spam',
      'copyright_violation', 'inappropriate_content', 'rule_violation',
      'fake_content', 'other'
    ];
    if (!validTypes.includes(reportType)) {
      return res.status(400).json({ message: 'Invalid report type' });
    }

    const reportData = {
      reporterId,
      reportType,
      description: description.trim(),
      reportedUserId,
      reportedGameId,
      reportedReviewId
    };

    const report = await ReportService.createReport(reportData);
    res.status(201).json({ 
      message: 'Report submitted successfully. A ticket has been created for review.', 
      report 
    });
  } catch (error) {
    console.error('Error creating report:', error);
    if (error.message === 'You have already reported this content') {
      res.status(400).json({ message: error.message });
    } else {
      res.status(500).json({ message: 'Server error while creating report' });
    }
  }
};

/**
 * Get reports (staff only)
 */
exports.getReports = async (req, res) => {
  try {
    const {
      reportType,
      status,
      reviewedBy,
      page = 1,
      limit = 20,
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = req.query;

    const filters = {};
    const options = {
      limit: parseInt(limit),
      offset: (parseInt(page) - 1) * parseInt(limit),
      sortBy,
      sortOrder
    };

    // Apply filters
    if (reportType) filters.reportType = reportType;
    if (status) filters.status = status;
    if (reviewedBy) filters.reviewedBy = reviewedBy;

    const reports = await ReportService.getReports(filters, options);

    // Get total count for pagination
    const totalCount = await ReportService.getReportsCount(filters);
    const totalPages = Math.ceil(totalCount / parseInt(limit));

    res.json({
      reports,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages,
      total: totalCount
    });
  } catch (error) {
    console.error('Error getting reports:', error);
    res.status(500).json({ message: 'Server error while fetching reports' });
  }
};

/**
 * Get report by ID (staff only)
 */
exports.getReportById = async (req, res) => {
  try {
    const { reportId } = req.params;
    const report = await ReportService.getReportById(reportId);
    res.json(report);
  } catch (error) {
    console.error('Error getting report:', error);
    if (error.message === 'Report not found') {
      res.status(404).json({ message: 'Report not found' });
    } else {
      res.status(500).json({ message: 'Server error while fetching report' });
    }
  }
};

/**
 * Resolve a report (staff only)
 */
exports.resolveReport = async (req, res) => {
  try {
    const { reportId } = req.params;
    const { resolutionNotes, action } = req.body;
    const reviewerId = req.user.userId;

    if (!resolutionNotes || resolutionNotes.trim().length === 0) {
      return res.status(400).json({ message: 'Resolution notes are required' });
    }

    const report = await ReportService.resolveReport(
      reportId, 
      reviewerId, 
      resolutionNotes.trim(), 
      action
    );
    
    res.json({ message: 'Report resolved successfully', report });
  } catch (error) {
    console.error('Error resolving report:', error);
    res.status(500).json({ message: 'Server error while resolving report' });
  }
};

/**
 * Dismiss a report (staff only)
 */
exports.dismissReport = async (req, res) => {
  try {
    const { reportId } = req.params;
    const { reason } = req.body;
    const reviewerId = req.user.userId;

    if (!reason || reason.trim().length === 0) {
      return res.status(400).json({ message: 'Dismissal reason is required' });
    }

    const report = await ReportService.dismissReport(reportId, reviewerId, reason.trim());
    res.json({ message: 'Report dismissed successfully', report });
  } catch (error) {
    console.error('Error dismissing report:', error);
    res.status(500).json({ message: 'Server error while dismissing report' });
  }
};

/**
 * Get user's own reports
 */
exports.getUserReports = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = req.query;
    const userId = req.user.userId;

    const filters = { reporterId: userId };
    const options = {
      limit: parseInt(limit),
      offset: (parseInt(page) - 1) * parseInt(limit),
      sortBy,
      sortOrder
    };

    const reports = await ReportService.getReports(filters, options);
    res.json({ reports, page: parseInt(page), limit: parseInt(limit) });
  } catch (error) {
    console.error('Error getting user reports:', error);
    res.status(500).json({ message: 'Server error while fetching your reports' });
  }
};

/**
 * Get report statistics (staff only)
 */
exports.getReportStats = async (req, res) => {
  try {
    const { dateFrom, dateTo } = req.query;
    
    const filters = {};
    if (dateFrom) filters.dateFrom = dateFrom;
    if (dateTo) filters.dateTo = dateTo;

    const stats = await ReportService.getReportStats(filters);
    res.json(stats);
  } catch (error) {
    console.error('Error getting report stats:', error);
    res.status(500).json({ message: 'Server error while fetching report statistics' });
  }
};

/**
 * Update report status (staff only)
 */
exports.updateReportStatus = async (req, res) => {
  try {
    const { reportId } = req.params;
    const { status } = req.body;
    const reviewerId = req.user.userId;

    const validStatuses = ['pending', 'under_review', 'resolved', 'dismissed'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({ message: 'Invalid status' });
    }

    await db('reports').where('id', reportId).update({
      status,
      reviewed_by: status !== 'pending' ? reviewerId : null,
      updated_at: new Date()
    });

    const report = await ReportService.getReportById(reportId);
    res.json({ message: 'Report status updated successfully', report });
  } catch (error) {
    console.error('Error updating report status:', error);
    res.status(500).json({ message: 'Server error while updating report status' });
  }
};
